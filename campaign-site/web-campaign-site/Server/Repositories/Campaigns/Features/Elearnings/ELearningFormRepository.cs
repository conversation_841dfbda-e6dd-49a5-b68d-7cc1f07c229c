using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Storage.Builders;
using Newtonsoft.Json;
using web_campaign_site.Server.Models.LearningManagementSystem;

namespace Motivai.WebCampaignSite.Server.Repositories
{
	public class ELearningFormRepository
	{
		private readonly FileDownloadUrlBuilder urlBuilder;

		public ELearningFormRepository(FileDownloadUrlBuilder urlBuilder)
		{
			this.urlBuilder = urlBuilder;
		}

		public async Task<List<dynamic>> GetActiveElearningsForms(Guid campaignId, string userId, PersonType? personType)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.LearningManagementSystem)
				.Path("site/campaigns").Path(campaignId).Path("features/elearnings/active")
				.Query("userId", userId)
				.Query("personType", personType?.ToString())
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();
			if (apiReturn.HasNullReturn())
				return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetElearningFormByShortUrlId(Guid campaignId, string shortUrlId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.LearningManagementSystem)
				.Path("site/campaigns").Path(campaignId).Path("features/elearnings")
				.Path(shortUrlId).Path("active")
				.AsyncGet()
				.GetApiReturn<ElearningForm>();
			if (apiReturn.HasNullReturn())
				return null;
			var elearningForm = apiReturn.GetReturnOrError();
			elearningForm.Modules.ForEach(module => {
				module.Contents.ForEach(content => {
					if (!string.IsNullOrEmpty(content.PdfUrl))
					{
						content.PdfUrl = urlBuilder.BuildPublicUrlFromInternalUrl(content.PdfUrl);
					}
					if (!string.IsNullOrEmpty(content.ExtraFile?.FileUrl))
					{
						content.ExtraFile.FileUrl = urlBuilder.BuildPublicUrlFromInternalUrl(content.ExtraFile.FileUrl);
					}
				});
			});
			return elearningForm;
		}

		public async Task<dynamic> GetElearlingModuleByShortUrlId(Guid campaignId, string elearningShortUrlId, string moduleShortUrlId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.LearningManagementSystem)
				.Path("site/campaigns").Path(campaignId).Path("features/elearnings").Path(elearningShortUrlId)
				.Path("modules").Path(moduleShortUrlId).Path("active")
				.AddXApiKeyFromProperties()
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn.HasNullReturn())
				return null;
			var elearningModule = apiReturn.GetReturnOrError();

				Console.WriteLine("elearningModule.contents " + JsonConvert.SerializeObject(elearningModule.contents));
			if (elearningModule == null && elearningModule.contents.Size > 0) {
				Console.WriteLine("elearningModule.contents " + elearningModule.contents);
			elearningModule.contents.ForEach((Action<dynamic>)(content => {
					if (!string.IsNullOrEmpty(content.pdfUrl))
					{
						content.pdfUrl = urlBuilder.BuildPublicUrlFromInternalUrl(content.pdfUrl);
					}
					if (!string.IsNullOrEmpty(content.extraFile?.fileUrl))
					{
						content.extraFile.fileUrl = urlBuilder.BuildPublicUrlFromInternalUrl(content.extraFile.fileUrl);
					}
			}));
			}
			return elearningModule;
		}

		public async Task<dynamic> FindNextModuleByActualShortUrlId(Guid campaignId, string elearningShortUrlId, string moduleShortUrlId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.LearningManagementSystem)
				.Path("site/campaigns").Path(campaignId).Path("features/elearnings").Path(elearningShortUrlId)
				.Path("modules").Path(moduleShortUrlId).Path("next/active")
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn.HasNullReturn())
				return null;
			return apiReturn.GetReturnOrError();
		}
	}
}